<?php
/**
 * Xdebug 调试测试文件
 * 
 * 使用方法：
 * 1. 在下面的代码行设置断点
 * 2. 启动 VSCode 调试器 (F5)
 * 3. 在浏览器中访问此文件，URL 后加 ?XDEBUG_SESSION_START=1
 */

// 设置断点在这一行
$message = "Hello Xdebug!";
echo "<h1>Xdebug 调试测试</h1>";

// 测试变量
$testArray = [
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'age' => 25,
    'hobbies' => ['reading', 'coding', 'gaming']
];

// 设置断点在这一行
$result = processUserData($testArray);

echo "<h2>处理结果:</h2>";
echo "<pre>" . print_r($result, true) . "</pre>";

// 测试函数调用
function processUserData($userData) {
    // 设置断点在这一行
    $processed = [];
    
    foreach ($userData as $key => $value) {
        if (is_array($value)) {
            $processed[$key] = implode(', ', $value);
        } else {
            $processed[$key] = strtoupper($value);
        }
    }
    
    // 设置断点在这一行
    $processed['processed_at'] = date('Y-m-d H:i:s');
    
    return $processed;
}

// 测试错误处理
try {
    // 设置断点在这一行
    $division = 10 / 2;
    echo "<h2>计算结果: $division</h2>";
    
    // 故意制造一个警告
    $undefinedVar = $nonExistentVariable ?? 'default value';
    echo "<h2>未定义变量处理: $undefinedVar</h2>";
    
} catch (Exception $e) {
    echo "<h2>错误: " . $e->getMessage() . "</h2>";
}

// 显示 Xdebug 信息
echo "<h2>Xdebug 信息:</h2>";
if (extension_loaded('xdebug')) {
    echo "<p style='color: green;'>✓ Xdebug 已加载</p>";
    echo "<p>Xdebug 版本: " . phpversion('xdebug') . "</p>";
    
    // 显示 Xdebug 配置
    $xdebugConfig = [
        'xdebug.mode' => ini_get('xdebug.mode'),
        'xdebug.client_host' => ini_get('xdebug.client_host'),
        'xdebug.client_port' => ini_get('xdebug.client_port'),
        'xdebug.start_with_request' => ini_get('xdebug.start_with_request'),
    ];
    
    echo "<h3>Xdebug 配置:</h3>";
    echo "<pre>" . print_r($xdebugConfig, true) . "</pre>";
} else {
    echo "<p style='color: red;'>✗ Xdebug 未加载</p>";
}

// 显示 PHP 信息
echo "<h2>PHP 信息:</h2>";
echo "<p>PHP 版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

// 显示请求信息
echo "<h2>请求信息:</h2>";
echo "<pre>" . print_r($_SERVER, true) . "</pre>";
?>
