@echo off
echo ==========================================
echo 获取 Windows 主机 IP 地址
echo ==========================================
echo.

echo 方法1: 查看所有网络适配器
echo ==========================================
ipconfig | findstr /C:"IPv4"
echo.

echo 方法2: 查看 WSL 相关适配器
echo ==========================================
ipconfig | findstr /C:"WSL" -A 5 -B 2
echo.

echo 方法3: 查看 vEthernet 适配器
echo ==========================================
ipconfig | findstr /C:"vEthernet" -A 5 -B 2
echo.

echo 方法4: 从 WSL2 内部获取网关 IP
echo ==========================================
wsl ip route show ^| grep default ^| awk "{print $3}"
echo.

echo ==========================================
echo 使用说明:
echo ==========================================
echo 1. 找到上面显示的 IP 地址（通常是 172.x.x.x 格式）
echo 2. 在 WSL2 中运行: ./fix_xdebug_host.sh
echo 3. 输入找到的 IP 地址
echo.
echo 或者手动编辑 /etc/php/8.2/mods-available/xdebug.ini
echo 将 xdebug.client_host 设置为正确的 IP 地址
echo.
pause
