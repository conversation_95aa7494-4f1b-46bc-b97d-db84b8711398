# 🚨 WSL2 Xdebug 连接问题快速修复指南

## 问题描述
`host.docker.internal` 无法解析，导致 Xdebug 无法连接到 VSCode。

## 🔧 快速解决方案

### 步骤 1: 获取 Windows 主机 IP

**在 Windows 命令行中运行：**
```cmd
ipconfig
```

**或者运行我们提供的脚本：**
```cmd
get_windows_ip.bat
```

**查找类似这样的 IP 地址：**
- `**********`
- `**********` 
- `**********`
- `**********`
- 等等...

通常在 "vEthernet (WSL)" 或类似的适配器下。

### 步骤 2: 自动修复（推荐）

**在 WSL2 中运行：**
```bash
./fix_xdebug_host.sh
```

脚本会自动：
1. 检测 Windows 主机 IP
2. 备份现有配置
3. 更新 Xdebug 配置
4. 重启相关服务

### 步骤 3: 手动修复（备选）

**如果自动修复失败，手动编辑配置文件：**

```bash
sudo nano /etc/php/8.2/mods-available/xdebug.ini
```

**将内容修改为：**
```ini
zend_extension=xdebug.so
xdebug.mode=debug
xdebug.client_host=172.XX.XX.1  ; 替换为您的实际 IP
xdebug.client_port=9003
xdebug.start_with_request=trigger
xdebug.idekey=VSCODE
xdebug.log=/var/log/xdebug.log
```

**重启服务：**
```bash
sudo service php8.2-fpm restart
sudo service nginx restart
```

### 步骤 4: 配置 Windows 防火墙

**在 Windows PowerShell（管理员）中运行：**
```powershell
netsh advfirewall firewall add rule name="WSL2 Xdebug" dir=in action=allow protocol=TCP localport=9003
```

### 步骤 5: 测试调试

1. **在 VSCode 中：**
   - 按 F5
   - 选择 "Listen for Xdebug (WSL2)"

2. **在浏览器中访问：**
   ```
   http://localhost/wsl2_debug_test.php?XDEBUG_SESSION_START=1
   ```

3. **设置断点并测试**

## 🔍 验证配置

**检查 Xdebug 配置：**
```bash
php -r "echo 'Client Host: ' . ini_get('xdebug.client_host') . \"\n\";"
```

**测试网络连接：**
```bash
ping 172.XX.XX.1  # 替换为您的实际 IP
```

**查看 Xdebug 日志：**
```bash
sudo tail -f /var/log/xdebug.log
```

## 🚨 常见问题

### 问题 1: 仍然无法连接
- 检查 Windows 防火墙设置
- 确认 IP 地址正确
- 重启 WSL2：`wsl --shutdown` 然后重新启动

### 问题 2: 路径映射错误
- 检查 VSCode launch.json 中的 pathMappings
- 确认项目路径正确

### 问题 3: 端口冲突
- 检查端口 9003 是否被占用
- 尝试使用其他端口（如 9000）

## 📝 备注

- 每次重启 Windows 后，IP 地址可能会变化
- 建议将修复脚本加入到启动脚本中
- 可以考虑使用静态 IP 配置

## 🆘 如果仍有问题

1. 运行完整的诊断脚本：`./wsl2_xdebug_check.sh`
2. 检查所有服务状态
3. 查看详细的错误日志
4. 考虑使用 Docker 环境作为替代方案
