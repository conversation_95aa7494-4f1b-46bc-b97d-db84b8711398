#!/bin/bash

echo "=========================================="
echo "修复 WSL2 Xdebug 主机连接问题"
echo "=========================================="
echo

# 方法1: 尝试获取 Windows 主机 IP
echo "1. 检测 Windows 主机 IP 地址..."

# 尝试多种方法获取主机 IP
WINDOWS_IP=""

# 方法1: 从路由表获取
if [ -z "$WINDOWS_IP" ]; then
    WINDOWS_IP=$(ip route show | grep default | awk '{print $3}' 2>/dev/null)
    if [ -n "$WINDOWS_IP" ]; then
        echo "从路由表获取到 IP: $WINDOWS_IP"
    fi
fi

# 方法2: 从 resolv.conf 获取
if [ -z "$WINDOWS_IP" ] && [ -f /etc/resolv.conf ]; then
    WINDOWS_IP=$(grep nameserver /etc/resolv.conf | awk '{print $2}' | head -1)
    if [ -n "$WINDOWS_IP" ]; then
        echo "从 resolv.conf 获取到 IP: $WINDOWS_IP"
    fi
fi

# 方法3: 尝试常见的 WSL2 IP 范围
if [ -z "$WINDOWS_IP" ]; then
    for ip in ********** ********** ********** ********** ********** ********** ********** **********; do
        if ping -c 1 -W 1 $ip > /dev/null 2>&1; then
            WINDOWS_IP=$ip
            echo "通过 ping 测试找到可用 IP: $WINDOWS_IP"
            break
        fi
    done
fi

# 方法4: 手动输入
if [ -z "$WINDOWS_IP" ]; then
    echo "无法自动检测到 Windows 主机 IP"
    echo "请手动输入 Windows 主机 IP 地址:"
    echo "您可以在 Windows 命令行中运行 'ipconfig' 查看 WSL 适配器的 IP"
    read -p "请输入 IP 地址: " WINDOWS_IP
fi

if [ -z "$WINDOWS_IP" ]; then
    echo "错误: 无法获取 Windows 主机 IP 地址"
    exit 1
fi

echo "使用 Windows 主机 IP: $WINDOWS_IP"
echo

# 测试连接
echo "2. 测试连接到 Windows 主机..."
if ping -c 1 -W 3 $WINDOWS_IP > /dev/null 2>&1; then
    echo "✓ 可以连接到 Windows 主机 ($WINDOWS_IP)"
else
    echo "✗ 无法连接到 Windows 主机 ($WINDOWS_IP)"
    echo "请检查网络配置或防火墙设置"
fi
echo

# 查找 PHP 配置文件
echo "3. 查找 PHP 配置文件..."
PHP_INI_PATH=""

# 尝试多个可能的路径
for path in /etc/php/8.2/mods-available/xdebug.ini /etc/php/8.1/mods-available/xdebug.ini /etc/php/8.0/mods-available/xdebug.ini /etc/php/7.4/mods-available/xdebug.ini; do
    if [ -f "$path" ]; then
        PHP_INI_PATH="$path"
        echo "找到 Xdebug 配置文件: $PHP_INI_PATH"
        break
    fi
done

if [ -z "$PHP_INI_PATH" ]; then
    echo "未找到 Xdebug 配置文件，请手动指定路径:"
    read -p "请输入 xdebug.ini 文件路径: " PHP_INI_PATH
fi

if [ ! -f "$PHP_INI_PATH" ]; then
    echo "错误: 配置文件不存在: $PHP_INI_PATH"
    exit 1
fi

# 备份原配置文件
echo "4. 备份原配置文件..."
sudo cp "$PHP_INI_PATH" "${PHP_INI_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
echo "备份完成: ${PHP_INI_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
echo

# 更新配置文件
echo "5. 更新 Xdebug 配置..."
sudo tee "$PHP_INI_PATH" > /dev/null << EOF
zend_extension=xdebug.so
xdebug.mode=debug
xdebug.client_host=$WINDOWS_IP
xdebug.client_port=9003
xdebug.start_with_request=trigger
xdebug.idekey=VSCODE
xdebug.log=/var/log/xdebug.log
xdebug.log_level=7

; 其他有用的设置
xdebug.show_error_trace=1
xdebug.var_display_max_children=256
xdebug.var_display_max_data=1024
xdebug.var_display_max_depth=5
xdebug.max_nesting_level=512
EOF

echo "✓ Xdebug 配置已更新"
echo

# 重启服务
echo "6. 重启 PHP 服务..."
if systemctl is-active --quiet php8.2-fpm; then
    sudo systemctl restart php8.2-fpm
    echo "✓ PHP 8.2 FPM 已重启"
elif systemctl is-active --quiet php8.1-fpm; then
    sudo systemctl restart php8.1-fpm
    echo "✓ PHP 8.1 FPM 已重启"
elif systemctl is-active --quiet php8.0-fpm; then
    sudo systemctl restart php8.0-fpm
    echo "✓ PHP 8.0 FPM 已重启"
elif systemctl is-active --quiet php7.4-fpm; then
    sudo systemctl restart php7.4-fpm
    echo "✓ PHP 7.4 FPM 已重启"
else
    echo "⚠ 未检测到运行中的 PHP-FPM 服务"
fi

if systemctl is-active --quiet nginx; then
    sudo systemctl restart nginx
    echo "✓ Nginx 已重启"
elif systemctl is-active --quiet apache2; then
    sudo systemctl restart apache2
    echo "✓ Apache2 已重启"
fi

echo

# 验证配置
echo "7. 验证新配置..."
echo "当前 Xdebug 配置:"
php -r "
echo 'Mode: ' . ini_get('xdebug.mode') . \"\n\";
echo 'Client Host: ' . ini_get('xdebug.client_host') . \"\n\";
echo 'Client Port: ' . ini_get('xdebug.client_port') . \"\n\";
echo 'Start with Request: ' . ini_get('xdebug.start_with_request') . \"\n\";
"
echo

echo "=========================================="
echo "修复完成！"
echo "=========================================="
echo
echo "新的配置:"
echo "xdebug.client_host = $WINDOWS_IP"
echo "xdebug.client_port = 9003"
echo
echo "现在您可以:"
echo "1. 在 VSCode 中启动调试 (F5 -> Listen for Xdebug)"
echo "2. 访问: http://localhost/wsl2_debug_test.php?XDEBUG_SESSION_START=1"
echo
echo "如果仍有问题，请检查 Windows 防火墙设置。"
