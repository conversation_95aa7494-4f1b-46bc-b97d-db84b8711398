@echo off
echo ==========================================
echo WSL2 + Xdebug 环境检查
echo ==========================================
echo.

echo 正在 WSL2 中运行检查脚本...
echo.

wsl bash ./wsl2_xdebug_check.sh

echo.
echo ==========================================
echo Windows 端口检查
echo ==========================================

echo 检查 Windows 防火墙规则...
netsh advfirewall firewall show rule name="WSL2 Xdebug" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ WSL2 Xdebug 防火墙规则已存在
) else (
    echo ⚠ WSL2 Xdebug 防火墙规则不存在
    echo 建议运行以下命令（需要管理员权限）:
    echo netsh advfirewall firewall add rule name="WSL2 Xdebug" dir=in action=allow protocol=TCP localport=9003
)

echo.
echo 检查端口 9003 使用情况...
netstat -an | findstr :9003 >nul 2>&1
if %errorlevel% equ 0 (
    echo 端口 9003 使用情况:
    netstat -an | findstr :9003
) else (
    echo 端口 9003 未被占用
)

echo.
echo ==========================================
echo 完成！
echo ==========================================
pause
