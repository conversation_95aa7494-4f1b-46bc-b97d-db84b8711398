<?php
/**
 * WSL2 Xdebug 调试测试文件
 * 
 * 使用方法：
 * 1. 在 VSCode 中启动 "Listen for Xdebug (WSL2)" 调试
 * 2. 在下面的代码行设置断点
 * 3. 浏览器访问: http://localhost/wsl2_debug_test.php?XDEBUG_SESSION_START=1
 */

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>WSL2 Xdebug Test</title></head><body>";
echo "<h1>WSL2 + Xdebug 调试测试</h1>";

// 设置断点在这一行 - 测试基本变量
$wsl2_info = [
    'test_time' => date('Y-m-d H:i:s'),
    'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown',
    'script_filename' => __FILE__
];

echo "<h2>WSL2 环境信息:</h2>";
echo "<pre>" . print_r($wsl2_info, true) . "</pre>";

// 设置断点在这一行 - 测试函数调用
$result = testWSL2Function($wsl2_info);

echo "<h2>函数调用结果:</h2>";
echo "<pre>" . print_r($result, true) . "</pre>";

// 测试函数
function testWSL2Function($data) {
    // 设置断点在这一行
    $processed = [];
    
    foreach ($data as $key => $value) {
        // 设置断点在这一行 - 测试循环
        $processed['processed_' . $key] = strtoupper($value);
    }
    
    // 设置断点在这一行
    $processed['wsl2_path'] = getcwd();
    $processed['php_version'] = PHP_VERSION;
    
    return $processed;
}

// Xdebug 状态检查
echo "<h2>Xdebug 状态:</h2>";
if (extension_loaded('xdebug')) {
    echo "<p style='color: green; font-weight: bold;'>✓ Xdebug 已加载</p>";
    
    // 获取 Xdebug 配置
    $xdebug_config = [
        'version' => phpversion('xdebug'),
        'mode' => ini_get('xdebug.mode'),
        'client_host' => ini_get('xdebug.client_host'),
        'client_port' => ini_get('xdebug.client_port'),
        'start_with_request' => ini_get('xdebug.start_with_request'),
        'idekey' => ini_get('xdebug.idekey'),
        'log' => ini_get('xdebug.log')
    ];
    
    echo "<h3>Xdebug 配置:</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    foreach ($xdebug_config as $key => $value) {
        echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
    }
    echo "</table>";
    
} else {
    echo "<p style='color: red; font-weight: bold;'>✗ Xdebug 未加载</p>";
}

// 网络信息
echo "<h2>网络信息:</h2>";
$network_info = [
    'SERVER_ADDR' => $_SERVER['SERVER_ADDR'] ?? 'unknown',
    'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'unknown',
    'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'unknown'
];

echo "<table border='1' cellpadding='5' cellspacing='0'>";
foreach ($network_info as $key => $value) {
    echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
}
echo "</table>";

// 测试异常处理
echo "<h2>异常处理测试:</h2>";
try {
    // 设置断点在这一行
    $test_division = 10 / 2;
    echo "<p>正常计算: 10 / 2 = $test_division</p>";
    
    // 设置断点在这一行 - 测试异常
    if (isset($_GET['trigger_error'])) {
        throw new Exception("这是一个测试异常");
    }
    
} catch (Exception $e) {
    // 设置断点在这一行
    echo "<p style='color: red;'>捕获异常: " . $e->getMessage() . "</p>";
    echo "<p>文件: " . $e->getFile() . " 行号: " . $e->getLine() . "</p>";
}

// 调试会话信息
echo "<h2>调试会话信息:</h2>";
$debug_session = [
    'XDEBUG_SESSION' => $_GET['XDEBUG_SESSION'] ?? $_COOKIE['XDEBUG_SESSION'] ?? 'not set',
    'XDEBUG_SESSION_START' => $_GET['XDEBUG_SESSION_START'] ?? 'not set',
    'User Agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
];

echo "<table border='1' cellpadding='5' cellspacing='0'>";
foreach ($debug_session as $key => $value) {
    echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
}
echo "</table>";

// 测试链接
echo "<h2>测试链接:</h2>";
echo "<ul>";
echo "<li><a href='?XDEBUG_SESSION_START=1'>启动调试会话</a></li>";
echo "<li><a href='?XDEBUG_SESSION_START=1&trigger_error=1'>触发异常测试</a></li>";
echo "<li><a href='?XDEBUG_SESSION_STOP=1'>停止调试会话</a></li>";
echo "</ul>";

// 最后的断点测试
$final_message = "WSL2 Xdebug 测试完成！";
echo "<h2 style='color: green;'>$final_message</h2>";

echo "</body></html>";
?>
