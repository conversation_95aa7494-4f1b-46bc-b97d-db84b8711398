# Xdebug 调试配置指南

## 1. 安装 Xdebug

### Windows (XAMPP/WAMP)
1. 下载适合您 PHP 版本的 Xdebug 扩展：https://xdebug.org/download
2. 将 `php_xdebug.dll` 放入 PHP 扩展目录
3. 在 `php.ini` 中添加配置

### Linux/Mac
```bash
# 使用 PECL 安装
pecl install xdebug

# 或使用包管理器
# Ubuntu/Debian
sudo apt-get install php-xdebug

# CentOS/RHEL
sudo yum install php-xdebug
```

## 2. PHP 配置

### 找到 php.ini 文件
```bash
php --ini
```

### 添加 Xdebug 配置
将 `xdebug-config-template.ini` 中的内容添加到您的 `php.ini` 文件末尾。

### 验证安装
```bash
php -m | grep xdebug
```

## 3. VSCode 配置

### 安装 PHP Debug 扩展
1. 打开 VSCode
2. 安装 "PHP Debug" 扩展 (<PERSON>)

### 配置已完成
- `.vscode/launch.json` 已配置好三种调试模式
- 可以直接使用

## 4. 使用方法

### 方法 1: 监听调试 (推荐)
1. 在 VSCode 中按 F5 或选择 "Listen for Xdebug"
2. 在浏览器 URL 后添加 `?XDEBUG_SESSION_START=1`
3. 或安装浏览器扩展自动添加调试参数

### 方法 2: 直接调试当前文件
1. 打开要调试的 PHP 文件
2. 选择 "Launch currently open script"
3. 按 F5 开始调试

### 方法 3: 内置服务器调试
1. 选择 "Launch Built-in web server"
2. 按 F5 启动内置服务器并开始调试

## 5. 浏览器扩展 (可选)

### Chrome
- Xdebug helper: https://chrome.google.com/webstore/detail/xdebug-helper/eadndfjplgieldjbigjakmdgkmoaaaoc

### Firefox
- Xdebug Helper: https://addons.mozilla.org/en-US/firefox/addon/xdebug-helper-for-firefox/

## 6. 常见问题

### 调试器无法连接
1. 检查防火墙是否阻止端口 9003
2. 确认 Xdebug 配置正确
3. 查看 Xdebug 日志文件

### 断点不生效
1. 确认路径映射正确
2. 检查文件编码是否为 UTF-8
3. 确认断点设置在可执行代码行

### Docker 环境调试
如果使用 Docker，需要特殊配置：
```ini
xdebug.client_host=host.docker.internal  ; Windows/Mac
xdebug.client_host=**********           ; Linux
```

## 7. 测试调试

创建测试文件 `debug_test.php`：
```php
<?php
$message = "Hello Xdebug!";
echo $message;
var_dump($_SERVER);
?>
```

在 `echo $message;` 行设置断点，然后运行调试。

## 8. 性能优化

生产环境建议：
```ini
xdebug.mode=off
```

开发环境建议：
```ini
xdebug.mode=debug,develop
xdebug.start_with_request=trigger
```
