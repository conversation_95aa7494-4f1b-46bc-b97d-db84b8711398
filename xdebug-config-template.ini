[xdebug]
; Xdebug 3.x 配置 (推荐)
; 启用 Xdebug
zend_extension=xdebug

; 设置 Xdebug 模式
; debug: 启用调试功能
; develop: 启用开发助手功能（如 var_dump 美化）
; coverage: 启用代码覆盖率
; profile: 启用性能分析
; trace: 启用函数调用跟踪
xdebug.mode=debug,develop

; 调试客户端设置
xdebug.client_host=localhost
xdebug.client_port=9003

; 启动设置
; yes: 总是启动调试会话
; no: 不自动启动
; trigger: 只有在触发器存在时才启动（推荐）
xdebug.start_with_request=trigger

; IDE 密钥（可选，用于多用户环境）
xdebug.idekey=VSCODE

; 日志设置（用于调试 Xdebug 本身）
xdebug.log=/tmp/xdebug.log
xdebug.log_level=7

; 其他有用的设置
; 显示错误
xdebug.show_error_trace=1

; 变量显示设置
xdebug.var_display_max_children=256
xdebug.var_display_max_data=1024
xdebug.var_display_max_depth=5

; 性能设置
xdebug.max_nesting_level=512

; 如果使用 Docker 或远程调试，可能需要以下设置：
; xdebug.client_host=host.docker.internal  ; Docker for Windows/Mac
; xdebug.client_host=**********           ; Docker for Linux
; xdebug.discover_client_host=1           ; 自动发现客户端主机

; 对于 Xdebug 2.x (旧版本) 的配置：
; xdebug.remote_enable=1
; xdebug.remote_autostart=0
; xdebug.remote_host=localhost
; xdebug.remote_port=9003
; xdebug.remote_handler=dbgp
; xdebug.idekey=VSCODE
