# WSL2 + Xdebug 调试配置指南

## 当前配置状态 ✅

您的 WSL2 Xdebug 配置：
```ini
zend_extension=xdebug.so
xdebug.mode=debug
xdebug.client_host=host.docker.internal
xdebug.client_port=9003
xdebug.start_with_request=trigger
xdebug.idekey=PHPSTORM
xdebug.log=/var/log/xdebug.log
```

## 验证配置

### 1. 检查 Xdebug 是否正确加载
```bash
# 在 WSL2 中运行
php -m | grep xdebug
php --version
```

### 2. 检查 Xdebug 配置
```bash
php -i | grep xdebug
```

### 3. 测试网络连接
```bash
# 测试从 WSL2 到 Windows 的连接
ping host.docker.internal
```

## VSCode 配置说明

已更新的 `.vscode/launch.json` 包含 4 种调试模式：

### 1. Listen for Xdebug (WSL2) - 推荐
- 监听来自 WSL2 的调试连接
- 支持多种路径映射
- 适用于 Web 应用调试

### 2. Launch WSL PHP Script
- 直接在 WSL2 中运行 PHP 脚本
- 适用于命令行脚本调试

### 3. WSL Built-in Server Debug
- 在 WSL2 中启动内置服务器
- 自动打开浏览器

### 4. Debug Current File (Direct)
- 直接调试当前打开的文件

## 使用步骤

### 方法 1: Web 应用调试（推荐）

1. **启动 VSCode 调试器**
   ```
   按 F5 → 选择 "Listen for Xdebug (WSL2)"
   ```

2. **在代码中设置断点**

3. **触发调试会话**
   - 浏览器访问：`http://localhost/your-file.php?XDEBUG_SESSION_START=1`
   - 或使用浏览器扩展
   - 或在代码中添加：`xdebug_break();`

### 方法 2: 使用内置服务器

1. **启动调试配置**
   ```
   按 F5 → 选择 "WSL Built-in Server Debug"
   ```

2. **自动打开浏览器**，访问测试页面

## 路径映射说明

配置中包含多种可能的路径映射：
```json
"pathMappings": {
    "/mnt/d/www/rp-field": "${workspaceFolder}",
    "/var/www/html": "${workspaceFolder}",
    "/home/<USER>/www/rp-field": "${workspaceFolder}"
}
```

根据您的实际 WSL2 路径调整这些映射。

## 常见问题解决

### 1. 调试器无法连接

**检查防火墙**
```bash
# Windows 防火墙可能阻止连接
# 在 Windows PowerShell (管理员) 中运行：
New-NetFirewallRule -DisplayName "WSL2 Xdebug" -Direction Inbound -Protocol TCP -LocalPort 9003 -Action Allow
```

**检查 WSL2 网络**
```bash
# 在 WSL2 中检查网络配置
ip route show
cat /etc/resolv.conf
```

### 2. 路径映射问题

**查看实际路径**
```bash
# 在 WSL2 中查看当前路径
pwd
realpath .
```

**更新路径映射**
在 `launch.json` 中更新 `pathMappings`

### 3. Xdebug 日志检查

```bash
# 查看 Xdebug 日志
sudo tail -f /var/log/xdebug.log
```

### 4. 端口冲突

如果 9003 端口被占用：
```bash
# 检查端口使用情况
netstat -tulpn | grep 9003
```

## 测试调试

### 创建测试文件
```php
<?php
// test_debug.php
$message = "WSL2 Xdebug Test";
echo $message; // 在这里设置断点
phpinfo();
?>
```

### 测试步骤
1. 在 `echo $message;` 行设置断点
2. 启动 "Listen for Xdebug (WSL2)" 调试
3. 浏览器访问：`http://localhost/test_debug.php?XDEBUG_SESSION_START=1`

## 性能优化

### 开发环境配置
```ini
xdebug.mode=debug,develop
xdebug.start_with_request=trigger
xdebug.max_nesting_level=512
```

### 生产环境配置
```ini
xdebug.mode=off
```

## 浏览器扩展

推荐安装 Xdebug 浏览器扩展：
- **Chrome**: Xdebug helper
- **Firefox**: Xdebug Helper for Firefox

这样就不需要手动添加 `?XDEBUG_SESSION_START=1` 参数。

## 故障排除命令

```bash
# 重启 PHP-FPM (如果使用)
sudo systemctl restart php8.1-fpm

# 重启 Apache/Nginx
sudo systemctl restart apache2
sudo systemctl restart nginx

# 检查 PHP 配置文件位置
php --ini

# 验证 Xdebug 配置
php -dxdebug.mode=debug -dxdebug.start_with_request=yes -r "var_dump(extension_loaded('xdebug'));"
```
