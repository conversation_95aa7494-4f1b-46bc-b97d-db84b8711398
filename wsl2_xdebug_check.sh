#!/bin/bash

echo "=========================================="
echo "WSL2 + Xdebug 环境检查脚本"
echo "=========================================="
echo

# 检查 PHP 版本
echo "1. PHP 版本检查:"
php --version
echo

# 检查 Xdebug 扩展
echo "2. Xdebug 扩展检查:"
if php -m | grep -q xdebug; then
    echo "✓ Xdebug 扩展已加载"
    echo "Xdebug 版本: $(php -r 'echo phpversion("xdebug");')"
else
    echo "✗ Xdebug 扩展未加载"
fi
echo

# 检查 Xdebug 配置
echo "3. Xdebug 配置检查:"
echo "Mode: $(php -r 'echo ini_get("xdebug.mode");')"
echo "Client Host: $(php -r 'echo ini_get("xdebug.client_host");')"
echo "Client Port: $(php -r 'echo ini_get("xdebug.client_port");')"
echo "Start with Request: $(php -r 'echo ini_get("xdebug.start_with_request");')"
echo "IDE Key: $(php -r 'echo ini_get("xdebug.idekey");')"
echo "Log File: $(php -r 'echo ini_get("xdebug.log");')"
echo

# 检查网络连接
echo "4. 网络连接检查:"
if ping -c 1 host.docker.internal > /dev/null 2>&1; then
    echo "✓ 可以连接到 host.docker.internal"
else
    echo "✗ 无法连接到 host.docker.internal"
    echo "尝试其他方式..."
    
    # 获取 Windows 主机 IP
    WINDOWS_IP=$(ip route show | grep default | awk '{print $3}')
    echo "Windows 主机 IP: $WINDOWS_IP"
    
    if ping -c 1 $WINDOWS_IP > /dev/null 2>&1; then
        echo "✓ 可以连接到 Windows 主机 ($WINDOWS_IP)"
        echo "建议将 xdebug.client_host 设置为: $WINDOWS_IP"
    else
        echo "✗ 无法连接到 Windows 主机"
    fi
fi
echo

# 检查端口
echo "5. 端口检查:"
if netstat -tulpn 2>/dev/null | grep -q ":9003"; then
    echo "端口 9003 正在使用中:"
    netstat -tulpn | grep ":9003"
else
    echo "端口 9003 未被占用"
fi
echo

# 检查 PHP 配置文件
echo "6. PHP 配置文件:"
php --ini
echo

# 检查当前路径
echo "7. 当前路径信息:"
echo "当前目录: $(pwd)"
echo "真实路径: $(realpath .)"
echo

# 检查 Web 服务器状态
echo "8. Web 服务器检查:"
if systemctl is-active --quiet apache2; then
    echo "✓ Apache2 正在运行"
elif systemctl is-active --quiet nginx; then
    echo "✓ Nginx 正在运行"
else
    echo "⚠ 没有检测到运行中的 Web 服务器"
    echo "可以使用 PHP 内置服务器进行测试"
fi
echo

# 检查日志文件
echo "9. Xdebug 日志检查:"
XDEBUG_LOG=$(php -r 'echo ini_get("xdebug.log");')
if [ -n "$XDEBUG_LOG" ] && [ "$XDEBUG_LOG" != "0" ]; then
    if [ -f "$XDEBUG_LOG" ]; then
        echo "✓ Xdebug 日志文件存在: $XDEBUG_LOG"
        echo "最近的日志条目:"
        tail -5 "$XDEBUG_LOG" 2>/dev/null || echo "无法读取日志文件"
    else
        echo "⚠ Xdebug 日志文件不存在: $XDEBUG_LOG"
    fi
else
    echo "⚠ 未配置 Xdebug 日志文件"
fi
echo

# 测试 Xdebug 功能
echo "10. Xdebug 功能测试:"
php -r "
if (extension_loaded('xdebug')) {
    echo '✓ Xdebug 功能正常\n';
    if (function_exists('xdebug_info')) {
        echo '✓ xdebug_info() 函数可用\n';
    }
    if (function_exists('xdebug_break')) {
        echo '✓ xdebug_break() 函数可用\n';
    }
} else {
    echo '✗ Xdebug 功能异常\n';
}
"
echo

echo "=========================================="
echo "检查完成！"
echo "=========================================="
echo
echo "如果发现问题，请参考 WSL2_XDEBUG_GUIDE.md 进行故障排除。"
echo
echo "快速测试命令:"
echo "php -S 0.0.0.0:8000 -t . -dxdebug.mode=debug -dxdebug.start_with_request=trigger"
echo
echo "然后访问: http://localhost:8000/wsl2_debug_test.php?XDEBUG_SESSION_START=1"
